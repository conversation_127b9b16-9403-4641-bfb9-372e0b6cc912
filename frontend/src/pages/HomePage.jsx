import { useState, useEffect } from "react";
import { getAllCreditCardOffers, getAllBanks, getAllCategories } from "../services/api";
import CreditCardOffer from "../components/CreditCardOffer";
import BankFilter from "../components/BankFilter";
import CategoryFilter from "../components/CategoryFilter";
import SearchBar from "../components/SearchBar";
import LoadingSpinner from "../components/LoadingSpinner";
import "../styles/HomePage.css";

const HomePage = () => {
	const [offers, setOffers] = useState([]);
	const [banks, setBanks] = useState([]);
	const [categories, setCategories] = useState([]);
	const [selectedBank, setSelectedBank] = useState([]);
	const [selectedCategory, setSelectedCategory] = useState([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);

	// Pagination state
	const [currentPage, setCurrentPage] = useState(1);
	const [pagination, setPagination] = useState(null);
	const [loadingMore, setLoadingMore] = useState(false);

	// Fetch all banks
	useEffect(() => {
		const fetchBanks = async () => {
			try {
				const data = await getAllBanks();
				setBanks(data);
			} catch (err) {
				console.error("Failed to fetch banks:", err);
			}
		};

		fetchBanks();
	}, []);

	// Fetch all categories
	useEffect(() => {
		const fetchCategories = async () => {
			try {
				const data = await getAllCategories();
				setCategories(data);
			} catch (err) {
				console.error("Failed to fetch categories:", err);
			}
		};

		fetchCategories();
	}, []);

	// Debounce searchTerm
	useEffect(() => {
		const handler = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => {
			clearTimeout(handler);
		};
	}, [searchTerm]);

	// Reset pagination when filters change
	useEffect(() => {
		setCurrentPage(1);
		setPagination(null);
		setOffers([]); // Clear offers when filters change
	}, [selectedBank, selectedCategory, debouncedSearchTerm]);

	// Fetch offers based on filters (only for initial load and filter changes, not pagination)
	useEffect(() => {
		const fetchOffers = async () => {
			setLoading(true);
			setError(null);
			try {
				const filters = {
					bank: selectedBank,
					category: selectedCategory,
					search: debouncedSearchTerm,
					page: 1, // Always start from page 1 for initial load
					limit: 20,
				};

				const response = await getAllCreditCardOffers(filters);
				setOffers(response.offers || []);
				setPagination(response.pagination);
				setCurrentPage(1); // Ensure current page is set to 1
				setLoading(false);
			} catch (err) {
				setError(err.message || "Failed to fetch credit card offers. Please try again later.");
				setLoading(false);
			}
		};

		fetchOffers();
	}, [selectedBank, selectedCategory, debouncedSearchTerm]); // Removed currentPage from dependencies

	if (error) {
		return <div className="error">{error}</div>;
	}

	const handleBankSelect = (bank) => {
		setSelectedBank(bank);
	};

	const handleCategorySelect = (category) => {
		setSelectedCategory(category);
	};

	// Load more offers (pagination)
	const loadMoreOffers = async () => {
		if (!pagination?.hasNextPage || loadingMore) return;

		setLoadingMore(true);
		try {
			const filters = {
				bank: selectedBank,
				category: selectedCategory,
				search: debouncedSearchTerm,
				page: currentPage + 1,
				limit: 20,
			};

			const response = await getAllCreditCardOffers(filters);
			setOffers((prevOffers) => [...prevOffers, ...(response.offers || [])]);
			setPagination(response.pagination);
			setCurrentPage((prev) => prev + 1);
		} catch (err) {
			setError(err.message || "Failed to load more offers.");
		} finally {
			setLoadingMore(false);
		}
	};

	// Create filter description text
	const getFilterDescription = () => {
		const filters = [];
		if (selectedBank.length > 0) filters.push(`Banks: ${selectedBank.join(", ")}`);
		if (selectedCategory.length > 0) filters.push(`Categories: ${selectedCategory.join(", ")}`);
		if (searchTerm) filters.push(`Search: "${searchTerm}"`);

		if (filters.length === 0) return "Showing all offers.";
		return `Filtered by ${filters.join(" | ")}`;
	};

	return (
		<div className="home-page">
			<header className="header">
				<h1>Credit Card Offers in Sri Lanka</h1>
				<p>Compare the best credit card offers from various banks in Sri Lanka</p>
			</header>

			<div className="filters-and-search-row">
				<SearchBar value={searchTerm} onChange={setSearchTerm} />
				<BankFilter banks={banks} selectedBanks={selectedBank} onBankSelect={handleBankSelect} />
				<CategoryFilter
					categories={categories}
					selectedCategories={selectedCategory}
					onCategorySelect={handleCategorySelect}
				/>
			</div>

			{/* Display filter description and results count */}
			{!loading && (
				<div className="results-info">
					<p className="filter-description">{getFilterDescription()}</p>
					{pagination && (
						<p className="results-count">
							Showing {offers.length} of {pagination.totalCount} offers
							{pagination.totalPages > 1 &&
								` (Page ${pagination.currentPage} of ${pagination.totalPages})`}
						</p>
					)}
				</div>
			)}

			<div className="offers-container">
				{loading && (
					<div className="loading-overlay">
						<LoadingSpinner size="large" message="Loading credit card offers..." />
					</div>
				)}
				{!loading && offers.length === 0 ? (
					<p className="no-offers">No credit card offers available for the selected filters.</p>
				) : (
					<>
						{offers.map((offer) => (
							<CreditCardOffer key={offer._id || offer.id} offer={offer} />
						))}

						{/* Load More Button */}
						{pagination?.hasNextPage && (
							<div className="load-more-container">
								<button className="load-more-btn" onClick={loadMoreOffers} disabled={loadingMore}>
									{loadingMore ? (
										<>
											<LoadingSpinner size="small" />
											<span style={{ marginLeft: "8px" }}>Loading more...</span>
										</>
									) : (
										"Load More Offers"
									)}
								</button>
							</div>
						)}
					</>
				)}
			</div>
		</div>
	);
};

export default HomePage;
